import React, { useEffect, useState } from 'react';
import { CSVLink } from 'react-csv';
import { Box, Button, Table, Upload } from '@alifd/next';
import CONST from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import LzMsg from '@/components/LzMsg';
import LzDialog from '@/components/LzDialog';
import { config } from 'ice';

const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default ({ max = 0, value, uploadList, filterList, confirm, cancel }) => {
  const [loading] = useState(false);
  const [successPageInfo, setSuccessPageInfo] = useState(defaultPage);
  const [errorPageInfo, setErrorPageInfo] = useState(defaultPage);
  const [selectedList, setSelectedList] = useState(value);
  // 选中的key列表
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [uploadListHandleData, setUploadListHandleData] = useState({ success: [], fail: [] } as any);
  const [dio, setDio] = useState(false);
  const [resultsVisible, setResultsVisible] = useState(false);
  const [uploaderRef, setUploaderRef] = useState(false);
  const [csvData, setCsvData] = useState<any>([]);
  const [uploadResData, setUploadResData] = useState({
    successTotal: 0,
    errorTotal: 0,
    successList: [],
    errorList: [],
  });
  console.log(uploadListHandleData);
  const [skusListStore, setSkusListStore] = useState<any[]>([]);
  const [skusSuccessListStore, setSuccessSkusListStore] = useState<any[]>([]);

  // 导入成功列表分页Change事件
  const successHandlePage = (page) => {
    const successPageInfoStore = { ...successPageInfo };
    successPageInfoStore.pageNum = page.pageNum;
    successPageInfoStore.pageSize = page.pageSize;
    const showSuccessData = { ...uploadResData };

    let ls: any[] = [];
    const lsResult: any[][] = [];
    skusSuccessListStore.forEach((val, idx) => {
      if ((idx + 1) % successPageInfoStore.pageSize === 0) {
        ls.push(val);
        lsResult.push(ls);
        ls = [];
      } else {
        ls.push(val);
        if (idx === skusSuccessListStore.length - 1) {
          lsResult.push(ls);
        }
      }
    });
    showSuccessData.successList = lsResult[page.pageNum - 1] as any;
    setUploadResData(showSuccessData);
    setSuccessPageInfo(successPageInfoStore);
  };

  // 导入失败列表分页Change事件
  const errorHandlePage = (page) => {
    const errorPageInfoStore = { ...errorPageInfo };
    errorPageInfoStore.pageNum = page.pageNum;
    errorPageInfoStore.pageSize = page.pageSize;
    const showErrorData = { ...uploadResData };
    let le: any[] = [];
    const leResult: any[] = [];
    skusListStore.forEach((val, idx) => {
      if ((idx + 1) % errorPageInfoStore.pageSize === 0) {
        le.push(val);
        leResult.push(le);
        le = [];
      } else {
        le.push(val);
        if (idx === skusListStore.length - 1) {
          leResult.push(le);
        }
      }
    });
    showErrorData.errorList = leResult[page.pageNum - 1];
    setUploadResData(showErrorData);
    setErrorPageInfo(errorPageInfoStore);
  };

  function arrDistinctByProp(arr, prop) {
    return arr.filter(function (item, index, self) {
      return self.findIndex((el) => el[prop] === item[prop]) === index;
    });
  }

  const handleConfirm = () => {
    const res = arrDistinctByProp(selectedList, 'skuId');
    const list = res.filter((sku) => selectedRowKeys.includes(sku.skuId));
    confirm(list);
  };

  // 导入模板
  const importTemplate = () => {
    setDio(true);
  };

  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };

  // 导入模板-确定
  const onImpUpload = () => {
    (uploaderRef as any).startUpload();
  };

  const afterUpload = (data) => {
    setDio(false);
    setResultsVisible(true);
    // 处理失败列表数据
    data.errorList.forEach((val) => {
      skusListStore.push(val);
    });
    setSkusListStore(skusListStore);
    // 成功列表数据总数赋值
    const successPageInfoStore = { ...successPageInfo };
    successPageInfoStore.total = data.successList.length;
    setSuccessPageInfo(successPageInfoStore);

    // 成功列表数据分页处理
    interface Ls {
      jdPrice: string;
      skuId: number | string;
      skuMainPicture: string;
      skuName: string;
    }

    let ls: Ls[] = [];
    const lsResult: Ls[][] = [];
    setSuccessSkusListStore(data.successList);
    data.successList.forEach((val, idx) => {
      if ((idx + 1) % successPageInfoStore.pageSize === 0) {
        ls.push(val);
        lsResult.push(ls);
        ls = [];
      } else {
        ls.push(val);
        if (idx === data.successList.length - 1) {
          lsResult.push(ls);
        }
      }
    });
    // 失败列表导出数据
    const errorExportData: string[][] = [['skuId', '失败原因']];
    data.errorList.forEach((val): void => {
      const arr: [string, string] = [val.skuId, '商品不存在'];
      errorExportData.push(arr);
    });
    setCsvData(errorExportData);

    // 失败列表数据总数赋值
    const errorPageInfoStore = { ...errorPageInfo };
    errorPageInfoStore.total = skusListStore.length;
    setErrorPageInfo(errorPageInfoStore);
    // 失败列表数据分页处理
    let le: any[] = [];
    const leResult: any[] = [];
    skusListStore.forEach((val, idx) => {
      if ((idx + 1) % errorPageInfoStore.pageSize === 0) {
        le.push(val);
        leResult.push(le);
        le = [];
      } else {
        le.push(val);
        if (idx === data.errorList.length - 1) {
          leResult.push(le);
        }
      }
    });
    // 成功列表/失败列表分页处理数据赋值
    setUploadListHandleData({ success: lsResult, fail: leResult });
    // 将成功数据传回给父组件
    uploadList(data.successList);
    // 保存成功数据
    setSelectedRowKeys(data.successList?.map((sku) => sku.skuId));
    setSelectedList(data.successList);
    // 本页面相关数据赋值
    setUploadResData({
      successTotal: data.successList.length,
      errorTotal: data.errorList.length,
      successList: data.successList,
      errorList: data.errorList,
    });
  };

  useEffect(() => {
    if (uploadResData.successList.length !== 0) {
      setSelectedRowKeys(filterList?.map((sku) => sku.skuId));
      const data = { ...uploadResData };
      data.successList = filterList;
      setSelectedList(filterList);
      setUploadResData(data);
    }
  }, [filterList]);

  return (
    <div>
      <div>
        <div>备注说明：</div>
        <div>1.店铺需先下载【商品导入】Excel模板，自主导入待商品的SKUID，点击上传；</div>
        <div>2.如导入的商品SKUID有误，则该项商品信息不予展示；</div>
        <div>3.如导入Excel有误，可选择重新导入，新的商品列表会覆盖旧的商品列表；</div>
        <div>
          4.【重要提示】如同时使用“选择商品”和“自主导入商品”两个tab设置，使用顺序须知：需先使用“自主导入商品”功能导入商品列表，再使用“选择商品”做商品列表补充；
        </div>
        <div>5.自主导入最多导入{max || 500}个商品的SKUID；</div>
        <div style={{ color: 'red' }}>说明：导入成功的商品排序和文件模板内的商品顺序一致</div>
      </div>
      <div style={{ textAlign: 'right', margin: '10px 0 15px 0' }}>
        <a
          download="模板.xlsx"
          href="https://interact.oss-cn-beijing.aliyuncs.com/%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx"
        >
          <Button type="primary" style={{ marginRight: '10px' }}>
            下载模板
          </Button>
        </a>

        <Button type="primary" onClick={importTemplate}>
          导入模板
        </Button>
      </div>
      <Table.StickyLock
        loading={loading}
        primaryKey="skuId"
        fixedHeader
        maxBodyHeight={165}
        dataSource={uploadResData.successList}
      >
        <Table.Column title="商品名称" dataIndex="skuName" />
        <Table.Column title="京东价（元）" dataIndex="jdPrice" width={120} />
      </Table.StickyLock>
      <LzPagination
        total={successPageInfo.total}
        pageNum={successPageInfo.pageNum}
        pageSize={successPageInfo.pageSize}
        onChange={successHandlePage}
      />
      <br />
      <Box direction="row" justify="flex-end" align="center" spacing={10}>
        <Button
          type="primary"
          onClick={handleConfirm}
          disabled={max > 0 && (!selectedRowKeys.length || selectedRowKeys.length > max)}
        >
          确定({`${selectedRowKeys.length}${max > 0 ? `/${max}` : ''} `})
        </Button>
        <Button type="normal" onClick={cancel}>
          取消
        </Button>
      </Box>
      <LzDialog
        title="文件上传"
        footer={[
          <Button key="cancel" onClick={() => setDio(false)}>
            取消
          </Button>,
          <Button key="confirm" type="primary" style={{ marginRight: '10px' }} onClick={() => onImpUpload()}>
            确定
          </Button>,
        ]}
        visible={dio}
        onClose={() => setDio(false)}
      >
        <div style={{ width: 650, display: 'flex', alignItems: 'center' }}>
          <div>请上传文件：</div>
          <Upload
            action={`${config.baseURL}/sku/importSkuExcel`}
            name="file"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
            autoUpload={false}
            withCredentials
            headers={{
              token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
              prd: localStorage.getItem(CONST.LZ_SSO_PRD),
            }}
            ref={saveUploaderRef}
            listType="text"
            useDataURL
            limit={1}
            onError={(res) => {
              LzMsg.error(res.response?.message);
              setDio(false);
            }}
            onSuccess={(res) => {
              afterUpload(res.response?.data);
            }}
          >
            <Button type="secondary">上传文件</Button>
          </Upload>
        </div>
        <div style={{ color: '#ccc', paddingTop: '20px', paddingLeft: '70px' }}>
          注：导入文件为xlsx格式，请下载模板后用模板导入SKU
        </div>
      </LzDialog>
      <LzDialog
        className="dialog-box"
        title="查看结果"
        footer={[
          <Button key="1" onClick={() => setResultsVisible(false)}>
            关闭
          </Button>,
        ]}
        style={{ width: '850px' }}
        visible={resultsVisible}
        onClose={() => {
          setResultsVisible(false);
        }}
      >
        <div>
          <div style={{ marginBottom: '15px', display: 'flex', justifyContent: 'space-between' }}>
            <div style={{ height: '30px', lineHeight: '30px' }}>
              共{' '}
              <span style={{ color: '#39f' }}>
                {Number(uploadResData.successTotal) + Number(uploadResData.errorTotal)}
              </span>{' '}
              个SKU，成功 <span style={{ color: '#39f' }}>{uploadResData.successTotal}</span> 个，失败{' '}
              <span style={{ color: '#39f' }}>{uploadResData.errorTotal}</span> 个，添加失败商品列表
            </div>

            <CSVLink data={csvData} filename={'失败商品列表.xls'} target="_blank" className="btn btn-primary">
              <div
                style={{
                  width: '70px',
                  height: '30px',
                  lineHeight: '30px',
                  textAlign: 'center',
                  border: '1px solid #03c1fd',
                  borderRadius: '4px',
                  cursor: 'pointer',
                }}
              >
                导出
              </div>
            </CSVLink>
          </div>
          <Table.StickyLock
            loading={loading}
            primaryKey="skuId"
            fixedHeader
            maxBodyHeight={250}
            dataSource={uploadResData.errorList}
          >
            <Table.Column title="SKUID" dataIndex="skuId" align="center" />
            <Table.Column title="失败原因" dataIndex="reason" align="center" />
          </Table.StickyLock>
          <LzPagination
            total={errorPageInfo.total}
            pageNum={errorPageInfo.pageNum}
            pageSize={errorPageInfo.pageSize}
            onChange={errorHandlePage}
          />
        </div>
      </LzDialog>
    </div>
  );
};

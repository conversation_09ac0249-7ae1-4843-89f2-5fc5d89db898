/**
 * 大转盘抽奖数据报表
 */
import React, { useState } from 'react';
import { Button, Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import WinRecord from '@/pages/activity/96010/record/components/WinRecord';
import LotteryRecord from '@/pages/activity/96010/record/components/LotteryRecord';
import exportCombinedLogs from '@/utils/exportAll';
// import { dataLotteryLogExport, dataWinningLogExport } from '@/api/v96010';
import LzDocGuide from '@/components/LzDocGuide';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="下单盲盒数据报表" actions={<LzDocGuide />}>
        <Button
          onClick={() => {
            // todo
            // exportCombinedLogs([dataLotteryLogExport, dataWinningLogExport], '下单盲盒数据报表');
          }}
          style={{ float: 'right', position: 'relative', zIndex: 1000, marginTop: 8 }}
        >
          导出全部
        </Button>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="抽奖记录" key="1">
            <LotteryRecord />
          </Tab.Item>
          <Tab.Item title="中奖记录" key="2">
            <WinRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};

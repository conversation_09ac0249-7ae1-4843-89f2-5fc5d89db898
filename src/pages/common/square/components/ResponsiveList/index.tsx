import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@alifd/next';
import { withRouter } from 'react-router-dom';
import LzEmpty from '@/components/LzEmpty';
import { appHistory } from '@ice/stark-app';
import dayjs from 'dayjs';
import { deepCopy } from '@/utils';
import { initData } from '@/models/customizedPager';
import { CustomizedActivityTypeVo } from '@/api/types';
import styles from './style.module.scss';

const ResponsiveList = (props) => {
  const { isLoading, list, history } = props;
  const [showImgInfo, setShowImgInfo] = useState(false);
  const [imgInfo, setImgInfo] = useState({ cover: '', title: '' });
  // 活动管理页面
  const handleActivityManage = (act: any): void => {
    if (act.managePage && act.managePage.indexOf('http') === 0) {
      window.open(act.managePage, act.managePage);
      return;
    }
    if (act.id === 10058) {
      appHistory.push('/interact2/giftBag?a=132&activityType=10058');
    } else {
      const params = deepCopy(initData);
      params.activityType = [act.id] as any;
      history.push(`/activity?params=${encodeURIComponent(JSON.stringify(params))}`);
    }
  };
  // 活动创建页面
  const handleActivityCreate = (act: any): void => {
    if (act.createPage && act.createPage.indexOf('http') === 0) {
      window.open(act.createPage, act.createPage);
      return;
    }
    switch (act.version) {
      case 1:
        act.createPage && appHistory.push(act.createPage);
        break;
      case 2:
        appHistory.push(`/interact2/activity/select?activityType=${act.id}`);
        break;
      case 3:
        history.push(`/select?activityType=${act.id}`);
        break;
      default:
        break;
    }
    // if (act.version === 1) {
    //   act.createPage && appHistory.push(act.createPage);
    // } else  {
    //   history.push(`/select?activityType=${act.id}`);
    // }
  };
  const closeImgInfo = () => {
    setShowImgInfo(!showImgInfo);
    setImgInfo({ cover: '', title: '' });
  };

  // 格式化有效期显示
  const formatValidityPeriod = (item: CustomizedActivityTypeVo) => {
    const formatDate = (timestamp) => {
      if (!timestamp) return '';
      return dayjs(timestamp).format('YYYY/MM/DD');
    };

    // 只用红色标记过期状态，不添加文本
    const textStyle = !item.active ? { color: 'red' } : {};

    if (item.permanent) {
      return <span style={textStyle}>长期有效</span>;
    } else if (item.startTime && item.endTime) {
      return <span style={textStyle}>{`${formatDate(item.startTime)} 至 ${formatDate(item.endTime)}`}</span>;
    } else if (item.startTime) {
      return <span style={textStyle}>{`${formatDate(item.startTime)} 起`}</span>;
    } else if (item.endTime) {
      return <span style={textStyle}>{`截止至 ${formatDate(item.endTime)}`}</span>;
    }
    return <span style={textStyle}>未设置</span>;
  };

  return (
    <div className={styles.SquarePage}>
      <div className={['lz-flex-box', styles.ActivityTypeRows].join(' ')}>
        {list &&
          list.map((item: CustomizedActivityTypeVo) => (
            <Card key={item.id} free hasBorder className={styles.ActivityTypeCard}>
              <Card.Media>
                <img
                  loading={'lazy'}
                  src={item.cover}
                  className={styles.ActivityTypeCardCover}
                  alt={item.title}
                  onClick={() => {
                    setShowImgInfo(!showImgInfo);
                    setImgInfo({ cover: item.cover || '', title: item.title || '' });
                  }}
                />
              </Card.Media>
              <Card.Header
                title={
                  <div className={styles.ActivityTypeCardTitle}>
                    <div className={styles.ActivityTypeCardTitleText}>{item.title}</div>
                  </div>
                }
              />
              <Card.Content>
                <div className={styles.ActivityTypeContent}>
                  <div>编号：{item.id}</div>
                  <div>版本：V{item.version}</div>
                  <div>有效期：{formatValidityPeriod(item)}</div>
                </div>
              </Card.Content>
              <Card.Actions style={{ whiteSpace: 'nowrap', display: 'flex', justifyContent: 'space-around' }}>
                <Button
                  type="primary"
                  key="action1"
                  disabled={!item.enable || !item.active}
                  title={
                    !item.enable ? '活动不可用,请联系管理' : !item.active ? '活动未激活,暂不可创建' : '点击创建活动'
                  }
                  onClick={() => handleActivityCreate(item)}
                  style={{ width: '50%' }}
                >
                  创建活动
                </Button>
                <Button type="normal" key="action2" onClick={() => handleActivityManage(item)} style={{ width: '50%' }}>
                  管理活动
                </Button>
              </Card.Actions>
            </Card>
          ))}
      </div>
      {(!list || (list.length === 0 && !isLoading)) && <LzEmpty />}
      {showImgInfo && (
        <div className={styles.ImgInfo} onClick={closeImgInfo}>
          <img loading={'lazy'} src={imgInfo.cover} alt={imgInfo.title} className={styles.ShowImgInfo} />
          <div className={`iconfont icon-icon4-29 ${styles.closeBtn}`} onClick={closeImgInfo} />
        </div>
      )}
    </div>
  );
};

export default withRouter(ResponsiveList);
